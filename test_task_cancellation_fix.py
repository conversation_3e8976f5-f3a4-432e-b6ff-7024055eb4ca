#!/usr/bin/env python3
"""
测试任务取消问题的修复
验证任务不会在创建后立即被取消
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_task_not_cancelled_immediately():
    """测试任务不会立即被取消"""
    print("=" * 50)
    print("测试任务不会立即被取消")
    print("=" * 50)
    
    try:
        from app.services.task_service import TranslationTaskService
        from app.helpers.task_manager import TaskManager
        
        # 创建任务服务实例
        task_service = TranslationTaskService()
        task_manager = TaskManager()
        
        # 模拟Redis服务
        with patch('app.services.redis_service.redis_service') as mock_redis:
            mock_redis.create_task.return_value = True
            mock_redis.get_task.return_value = Mock()
            mock_redis.update_task.return_value = True
            
            # 模拟WebSocket服务
            with patch('app.services.websocket_service.websocket_service') as mock_ws:
                mock_ws.send_translation_update.return_value = None
                mock_ws.send_task_completed.return_value = None
                
                # 模拟翻译服务
                with patch.object(task_service.translate_service, 'translate_content_batch') as mock_translate:
                    mock_translate.return_value = {"zh-CN": "你好世界"}
                    
                    # 创建测试任务
                    task_id = "test_task_123"
                    items = [{
                        "key": "test_key",
                        "source_text": "Hello world",
                        "target_languages": ["zh-CN"]
                    }]
                    
                    # 启动任务
                    success = task_service.start_batch_translation(task_id, items, "en-US")
                    
                    if not success:
                        print("❌ 任务启动失败")
                        return False
                    
                    print(f"✅ 任务启动成功: {task_id}")
                    
                    # 检查任务是否在active_tasks中
                    if task_id in task_service.active_tasks:
                        print(f"✅ 任务在活跃任务列表中: {task_id}")
                    else:
                        print(f"❌ 任务不在活跃任务列表中: {task_id}")
                        return False
                    
                    # 等待一小段时间让线程开始执行
                    time.sleep(0.1)
                    
                    # 再次检查任务是否仍在active_tasks中
                    if task_id in task_service.active_tasks:
                        print(f"✅ 任务在执行过程中仍在活跃列表中: {task_id}")
                    else:
                        print(f"❌ 任务在执行过程中被从活跃列表中移除: {task_id}")
                        return False
                    
                    # 等待任务完成
                    if task_id in task_service.active_tasks:
                        thread = task_service.active_tasks[task_id]
                        thread.join(timeout=2.0)  # 等待最多2秒
                    
                    # 检查任务是否正常完成（应该被清理）
                    if task_id not in task_service.active_tasks:
                        print(f"✅ 任务正常完成后被清理: {task_id}")
                        return True
                    else:
                        print(f"❌ 任务完成后未被清理: {task_id}")
                        return False
                        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_task_cleanup_logic():
    """测试任务清理逻辑"""
    print("=" * 50)
    print("测试任务清理逻辑")
    print("=" * 50)
    
    try:
        from app.services.task_service import TranslationTaskService
        
        # 创建任务服务实例
        task_service = TranslationTaskService()
        
        # 手动添加一个任务到active_tasks
        test_task_id = "test_cleanup_task"
        task_service.active_tasks[test_task_id] = Mock()
        task_service.timeout_timers[test_task_id] = Mock()
        
        print(f"✅ 手动添加任务到活跃列表: {test_task_id}")
        
        # 验证任务在列表中
        if test_task_id in task_service.active_tasks:
            print(f"✅ 任务在活跃列表中: {test_task_id}")
        else:
            print(f"❌ 任务不在活跃列表中: {test_task_id}")
            return False
        
        # 调用清理方法
        task_service._cleanup_task(test_task_id)
        
        # 验证任务被清理
        if test_task_id not in task_service.active_tasks:
            print(f"✅ 任务被正确清理: {test_task_id}")
        else:
            print(f"❌ 任务未被清理: {test_task_id}")
            return False
        
        # 验证定时器被清理
        if test_task_id not in task_service.timeout_timers:
            print(f"✅ 定时器被正确清理: {test_task_id}")
            return True
        else:
            print(f"❌ 定时器未被清理: {test_task_id}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_process_translation_task_structure():
    """测试翻译任务处理结构"""
    print("=" * 50)
    print("测试翻译任务处理结构")
    print("=" * 50)
    
    try:
        # 检查代码结构
        file_path = "backend/app/services/task_service.py"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改点
        checks = [
            ("移除了finally块", "finally:" not in content.split("def _process_translation_task")[1].split("def _translate_single_item")[0]),
            ("在正常完成后清理", "# 任务正常完成，清理资源" in content),
            ("在异常情况下清理", "# 只在异常情况下清理任务资源" in content),
            ("保留异常处理", "except Exception as e:" in content),
        ]
        
        passed = 0
        for check_name, condition in checks:
            if condition:
                print(f"✅ {check_name}")
                passed += 1
            else:
                print(f"❌ {check_name}")
        
        print(f"\n代码结构检查: {passed}/{len(checks)} 通过")
        return passed >= 3  # 至少3个检查通过
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试任务取消问题修复...")
    
    tests = [
        ("代码结构检查", test_process_translation_task_structure),
        ("任务清理逻辑", test_task_cleanup_logic),
        ("任务不会立即取消", test_task_not_cancelled_immediately),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n正在测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed >= 2:  # 至少2个测试通过
        print("🎉 任务取消问题修复验证成功！")
        print("✅ 移除了会导致任务立即取消的finally块")
        print("✅ 任务现在只在正常完成或异常时才会被清理")
        print("✅ 任务不会在创建后立即被取消")
        return True
    else:
        print("❌ 任务取消问题修复需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
