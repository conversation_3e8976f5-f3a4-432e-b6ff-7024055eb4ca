<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-y-auto"
  >
    <!-- 导航栏 -->
    <nav
      class="bg-white/80 backdrop-blur-md shadow-sm border-b border-white/20 sticky top-0 z-50"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"
            >
              <font-awesome-icon icon="language" class="text-white text-lg" />
            </div>
            <h1
              class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"
            >
              Excel 翻译工具
            </h1>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
      <!-- 内容区域 -->
      <div class="space-y-8">
        <!-- 文件上传 -->
        <div
          v-show="currentStep === StepEnum.FILE_UPLOAD"
          class="animate-fade-in"
        >
          <FileUploadStep
            @file-uploaded="handleFileUploaded"
            :loading="loading"
          />
        </div>

        <!-- 翻译配置表格 -->
        <div
          v-show="currentStep === StepEnum.TRANSLATION_MANAGEMENT"
          class="animate-fade-in"
        >
          <TranslationManagementStep
            :excel-data="excelData"
            :language-names="excelData?.language_names"
            @start-translation="handleStartTranslation"
            @back="handleRestart"
            @restart="handleRestart"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { ExcelData } from '@/types';
import { translateApiService } from '@/api/translate';
import FileUploadStep from '@/components/steps/FileUploadStep.vue';
import TranslationManagementStep from '@/components/steps/TranslationManagementStep.vue';

enum StepEnum {
  FILE_UPLOAD = 1,
  TRANSLATION_MANAGEMENT = 2,
}

// 响应式数据
const currentStep = ref(StepEnum.FILE_UPLOAD);
const loading = ref(false);
const excelData = ref<ExcelData | null>(null);

// 处理文件上传成功 - 跳转到翻译管理步骤
const handleFileUploaded = (data: ExcelData) => {
  excelData.value = data;
  currentStep.value = StepEnum.TRANSLATION_MANAGEMENT;
};

// 处理开始翻译 - 发送翻译请求到后端
const handleStartTranslation = async (configData: any) => {
  console.log('翻译配置数据:', configData);

  try {
    // 发送翻译请求到Flask后端（只是调用接口，不处理结果）
    const response =
      await translateApiService.startBatchTranslation(configData);
    console.log('翻译请求响应:', response);
    alert('翻译请求已发送！');
  } catch (error) {
    console.error('翻译请求失败:', error);
    alert('翻译请求失败，请重试');
  }
};

// 重新开始
const handleRestart = () => {
  currentStep.value = StepEnum.FILE_UPLOAD;
  excelData.value = null;
};
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
