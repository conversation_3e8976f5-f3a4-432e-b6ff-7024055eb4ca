/**
 * 翻译模块API服务
 */
import request from '@/utils/request.helper';
import type {
  ExcelData,
  BatchTranslateRequest,
  BatchTranslateResponse,
  TranslationHistoryResponse,
  TranslationResultsResponse,
  Response,
} from './types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

class TranslateApiService {
  /**
   * 上传并解析Excel文件 - 只解析不翻译
   */
  async uploadFile(file: File): Promise<ExcelData> {
    const formData = new FormData();
    formData.append('file', file);

    return request.post(`${API_BASE_URL}/api/translate/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * 启动批量翻译任务
   */
  async startBatchTranslation(
    requestData: BatchTranslateRequest
  ): Promise<Response<BatchTranslateResponse>> {
    return request.post<BatchTranslateResponse>(
      `${API_BASE_URL}/api/translate/batch`,
      requestData
    );
  }

  /**
   * 获取翻译历史
   */
  async getHistory(
    limit: number = 50,
    offset: number = 0
  ): Promise<Response<TranslationHistoryResponse>> {
    return request.get<TranslationHistoryResponse>(
      `${API_BASE_URL}/api/history`,
      {
        params: { limit, offset },
      }
    );
  }

  /**
   * 获取翻译结果
   */
  async getTranslationResults(
    taskId: string
  ): Promise<Response<TranslationResultsResponse>> {
    return request.get<TranslationResultsResponse>(
      `${API_BASE_URL}/api/history/${taskId}/results`
    );
  }

  /**
   * 下载翻译结果文件
   */
  async downloadFile(taskId: string): Promise<Response<Blob>> {
    return request.get<Blob>(`${API_BASE_URL}/api/history/${taskId}/download`, {
      responseType: 'blob',
    });
  }

  /**
   * 删除翻译任务
   */
  async deleteTask(taskId: string): Promise<Response<void>> {
    return request.delete<void>(`${API_BASE_URL}/api/history/${taskId}`);
  }
}

export const translateApiService = new TranslateApiService();
export default translateApiService;
