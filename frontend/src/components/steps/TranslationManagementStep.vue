<template>
  <div class="max-w-full mx-auto">
    <!-- 页面标题 -->
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">翻译管理</h2>
      <p class="text-lg text-gray-600">配置翻译选项并选择要翻译的内容</p>
    </div>

    <!-- 翻译配置区域 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">翻译配置</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 源语言选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            源语言
          </label>
          <select
            v-model="config.sourceLanguage"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">自动检测</option>
            <option
              v-for="lang in sourceLanguageOptions"
              :key="lang.code"
              :value="lang.code"
            >
              {{ lang.name }}
            </option>
          </select>
        </div>

        <!-- 目标语言选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            目标语言
          </label>
          <div class="relative">
            <button
              @click="showTargetLanguageDropdown = !showTargetLanguageDropdown"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex items-center justify-between"
            >
              <span>{{ targetLanguageDisplayText }}</span>
              <svg
                class="w-4 h-4 transition-transform"
                :class="{ 'rotate-180': showTargetLanguageDropdown }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            <div
              v-if="showTargetLanguageDropdown"
              class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto"
            >
              <div class="p-2 border-b border-gray-200">
                <button
                  @click="selectAllTargetLanguages"
                  class="text-xs text-blue-600 hover:text-blue-800 mr-2"
                >
                  全选
                </button>
                <button
                  @click="clearAllTargetLanguages"
                  class="text-xs text-gray-600 hover:text-gray-800"
                >
                  清空
                </button>
              </div>
              <label
                v-for="lang in targetLanguageOptions"
                :key="lang.code"
                class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
              >
                <input
                  type="checkbox"
                  :value="lang.code"
                  v-model="config.targetLanguages"
                  class="mr-2 rounded"
                />
                {{ lang.name }}
              </label>
            </div>
          </div>
        </div>

        <!-- 翻译选项 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            翻译选项
          </label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="config.overwriteExisting"
                class="mr-2"
              />
              <span class="text-sm">覆盖现有翻译</span>
            </label>
            <label class="flex items-center">
              <input type="checkbox" v-model="config.skipEmpty" class="mr-2" />
              <span class="text-sm">跳过空值</span>
            </label>
            <label class="flex items-center">
              <input
                type="checkbox"
                v-model="config.preserveFormatting"
                class="mr-2"
              />
              <span class="text-sm">保持格式</span>
            </label>
          </div>
        </div>

        <!-- 批次设置 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            批次大小
          </label>
          <input
            type="number"
            v-model.number="config.batchSize"
            min="1"
            max="50"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p class="text-xs text-gray-500 mt-1">每批处理的条目数量</p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div></div>
        <div></div>
        <div></div>
        <div>
          <button
            @click="startBatchTranslation"
            :disabled="!canStartBatchTranslation"
            class="px-8 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {{ batchTranslationButtonText }}
          </button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- 表格头部操作栏 -->
      <div
        class="px-6 py-4 border-b border-gray-200 flex items-center justify-between"
      >
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              :checked="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="toggleSelectAll"
              class="mr-2"
            />
            <span class="text-sm font-medium">
              全选 ({{ selectedCount }}/{{ tableData.length }})
            </span>
          </label>
        </div>

        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">
            已选择 {{ selectedCount }} 项，将生成
            {{ estimatedTranslations }} 条翻译
          </span>
        </div>
      </div>

      <!-- 表格主体 -->
      <div class="overflow-auto max-h-[600px] sticky-table-container">
        <table class="w-full sticky-table">
          <thead class="bg-gray-50 sticky-header">
            <tr>
              <th
                class="sticky-column sticky-column-1 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12 bg-gray-50 border-r border-gray-200"
              >
                选择
              </th>
              <th
                class="sticky-column sticky-column-2 left-[62px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-48 bg-gray-50 border-r border-gray-200"
              >
                Key
              </th>
              <th
                v-for="language in displayLanguages"
                :key="language"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-48"
              >
                {{ getLanguageName(language) }}
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32"
              >
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="item in paginatedData"
              :key="item.key"
              :class="{ 'bg-blue-50': item.selected }"
            >
              <!-- 选择列 -->
              <td
                class="sticky-column sticky-column-1 px-6 py-4 whitespace-nowrap border-r border-gray-200"
                :class="item.selected ? 'bg-blue-50' : 'bg-white'"
              >
                <input
                  type="checkbox"
                  v-model="item.selected"
                  @change="updateSelectionStats"
                  class="rounded"
                />
              </td>

              <!-- Key列 -->
              <td
                class="sticky-column sticky-column-2 px-6 py-4 whitespace-nowrap border-r border-gray-200"
                :class="item.selected ? 'bg-blue-50' : 'bg-white'"
              >
                <div class="text-sm font-medium text-gray-900">
                  {{ item.key }}
                </div>
              </td>

              <!-- 各语言列 -->
              <td
                v-for="language in displayLanguages"
                :key="language"
                class="px-6 py-4"
              >
                <div class="text-sm text-gray-900">
                  <textarea
                    v-model="item.translations[language]"
                    class="w-full min-h-[2.5rem] px-2 py-1 border border-gray-300 rounded resize-none focus:outline-none focus:ring-1 focus:ring-blue-500"
                    :placeholder="
                      language === config.sourceLanguage ? '源文本' : '翻译内容'
                    "
                    rows="1"
                    @input="autoResize($event)"
                  ></textarea>
                </div>
              </td>

              <!-- 操作列 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex items-center space-x-2">
                  <button
                    @click="translateSingle(item)"
                    :disabled="!canTranslateSingle(item)"
                    class="text-blue-600 hover:text-blue-900 disabled:text-gray-400 disabled:cursor-not-allowed text-xs px-2 py-1 border border-blue-600 rounded hover:bg-blue-50 disabled:border-gray-300"
                  >
                    {{ getSingleTranslateButtonText(item) }}
                  </button>
                  <div
                    v-if="item.translating"
                    class="flex items-center text-xs text-gray-500"
                  >
                    <svg
                      class="animate-spin h-3 w-3 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    翻译中...
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div
        class="px-6 py-4 border-t border-gray-200 flex items-center justify-between"
      >
        <div class="text-sm text-gray-700">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} -
          {{ Math.min(currentPage * pageSize, tableData.length) }} 项， 共
          {{ tableData.length }} 项
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="currentPage--"
            :disabled="currentPage <= 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="text-sm">{{ currentPage }} / {{ totalPages }}</span>
          <button
            @click="currentPage++"
            :disabled="currentPage >= totalPages"
            class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="mt-6 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="$emit('back')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          返回
        </button>
        <button
          @click="$emit('restart')"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          重新开始
        </button>
      </div>

      <div class="flex items-center space-x-4">
        <div class="text-right">
          <div v-if="configValidationMessage" class="text-sm text-red-600 mb-2">
            {{ configValidationMessage }}
          </div>
          <button
            @click="startBatchTranslation"
            :disabled="!canStartBatchTranslation"
            class="px-8 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {{ batchTranslationButtonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import type { ExcelData } from '@/types';
import global from '@/utils/global.helper';
import { translateApiService } from '@/api/translate';
import type { BatchTranslateItem } from '@/api/translate/types';

// Props
const props = defineProps<{
  excelData: ExcelData | null;
  languageNames?: Record<string, string>;
}>();

// Emits
const emit = defineEmits<{
  startTranslation: [config: any];
  back: [];
  restart: [];
}>();

// 表格数据接口
interface TableRowData {
  key: string;
  selected: boolean;
  translations: Record<string, string>;
  translating?: boolean;
  lastTranslated?: string; // 最后翻译的目标语言
}

// 翻译配置
const config = ref({
  sourceLanguage: '',
  targetLanguages: [] as string[],
  overwriteExisting: false,
  skipEmpty: true,
  preserveFormatting: true,
  batchSize: 10,
});

// 表格数据
const tableData = ref<TableRowData[]>([]);
const currentPage = ref(1);
const pageSize = ref(50);
const showTargetLanguageDropdown = ref(false);
const batchTranslating = ref(false);

// 计算属性
const sourceLanguageOptions = computed(() => {
  if (!props.excelData) return [];
  return global.includeSourceLangList
    .filter((lang) => props.excelData!.languages.includes(lang))
    .map((lang) => ({
      code: lang,
      name: props.languageNames?.[lang] || lang,
    }));
});

const targetLanguageOptions = computed(() => {
  if (!props.excelData) return [];
  return props.excelData.languages
    .filter((lang) => lang !== config.value.sourceLanguage)
    .map((lang) => ({
      code: lang,
      name: props.languageNames?.[lang] || lang,
    }));
});

const displayLanguages = computed(() => {
  if (!props.excelData) return [];
  return props.excelData.languages;
});

const targetLanguageDisplayText = computed(() => {
  const count = config.value.targetLanguages.length;
  if (count === 0) return '请选择目标语言';
  if (count === 1) {
    const lang = config.value.targetLanguages[0];
    return props.languageNames?.[lang] || lang;
  }
  return `已选择 ${count} 种语言`;
});

const selectedCount = computed(() => {
  return tableData.value.filter((item) => item.selected).length;
});

const isAllSelected = computed(() => {
  return (
    tableData.value.length > 0 && selectedCount.value === tableData.value.length
  );
});

const isIndeterminate = computed(() => {
  return (
    selectedCount.value > 0 && selectedCount.value < tableData.value.length
  );
});

const estimatedTranslations = computed(() => {
  return selectedCount.value * config.value.targetLanguages.length;
});

const totalPages = computed(() => {
  return Math.ceil(tableData.value.length / pageSize.value);
});

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

const canStartBatchTranslation = computed(() => {
  return (
    !batchTranslating.value &&
    selectedCount.value > 0 &&
    config.value.targetLanguages.length > 0 &&
    config.value.sourceLanguage
  );
});

const batchTranslationButtonText = computed(() => {
  if (batchTranslating.value) return '翻译中...';
  if (selectedCount.value === 0) return '请选择要翻译的项目';
  if (!config.value.sourceLanguage) return '请选择源语言';
  if (config.value.targetLanguages.length === 0) return '请选择目标语言';
  return `批量翻译 (${selectedCount.value})`;
});

const configValidationMessage = computed(() => {
  if (!config.value.sourceLanguage) return '请选择源语言';
  if (config.value.targetLanguages.length === 0)
    return '请选择至少一种目标语言';
  if (selectedCount.value === 0) return '请选择要翻译的项目';
  return '';
});

// 方法
const getLanguageName = (languageCode: string) => {
  return props.languageNames?.[languageCode] || languageCode;
};

const initializeTableData = () => {
  if (!props.excelData) return;

  tableData.value = Object.keys(props.excelData.translations).map((key) => ({
    key,
    selected: true, // 默认全选
    translations: { ...props.excelData!.translations[key] },
  }));
};

const toggleSelectAll = () => {
  const newState = !isAllSelected.value;
  tableData.value.forEach((item) => {
    item.selected = newState;
  });
  updateSelectionStats();
};

const updateSelectionStats = () => {
  // 触发响应式更新
};

const selectAllTargetLanguages = () => {
  config.value.targetLanguages = targetLanguageOptions.value.map(
    (lang) => lang.code
  );
};

const clearAllTargetLanguages = () => {
  config.value.targetLanguages = [];
};

const autoResize = (event: Event) => {
  const textarea = event.target as HTMLTextAreaElement;
  textarea.style.height = 'auto';
  textarea.style.height = textarea.scrollHeight + 'px';
};

const canTranslateSingle = (item: TableRowData) => {
  return (
    config.value.sourceLanguage &&
    config.value.targetLanguages.length > 0 &&
    item.translations[config.value.sourceLanguage] &&
    !item.translating
  );
};

const getSingleTranslateButtonText = (item: TableRowData) => {
  if (item.translating) return '翻译中...';
  if (!config.value.sourceLanguage) return '需要源语言';
  if (config.value.targetLanguages.length === 0) return '需要目标语言';
  if (!item.translations[config.value.sourceLanguage]) return '需要源文本';
  return '翻译';
};

const translateSingle = async (item: TableRowData) => {
  if (!canTranslateSingle(item)) return;

  // 设置翻译状态
  item.translating = true;

  try {
    // 构建单条翻译请求数据
    const sourceText = item.translations[config.value.sourceLanguage];

    // 过滤需要翻译的目标语言
    const targetLanguages = config.value.targetLanguages.filter(
      (targetLang) => {
        // 如果已有翻译且不覆盖，则跳过
        return config.value.overwriteExisting || !item.translations[targetLang];
      }
    );

    if (targetLanguages.length === 0) {
      return; // 没有需要翻译的语言
    }

    // 调用真实的翻译API
    const batchRequest: BatchTranslateItem[] = [
      {
        key: item.key,
        source_text: sourceText,
        target_languages: targetLanguages,
      },
    ];

    const response = await translateApiService.startBatchTranslation({
      items: batchRequest,
      source_language: config.value.sourceLanguage,
    });

    if (response.data?.results?.[0]?.success) {
      const translations = response.data.results[0].translations;
      if (translations) {
        // 更新翻译结果
        Object.entries(translations).forEach(([lang, text]) => {
          item.translations[lang] = text as string;
          item.lastTranslated = lang;
        });
      }
    } else {
      console.error('翻译失败:', response.data?.results?.[0]?.error);
    }
  } catch (error) {
    console.error('单条翻译失败:', error);
    // 可以添加错误提示
  } finally {
    item.translating = false;
  }
};

const startBatchTranslation = async () => {
  if (!canStartBatchTranslation.value) return;

  // 构建批量翻译数据
  const selectedItems = tableData.value.filter((item) => item.selected);

  // 过滤掉空的源文本（如果配置了跳过空值）
  const validItems = selectedItems.filter((item) => {
    const sourceText = item.translations[config.value.sourceLanguage] || '';
    return !config.value.skipEmpty || sourceText.trim() !== '';
  });

  if (validItems.length === 0) {
    alert('没有有效的翻译项目，请检查源文本内容');
    return;
  }

  // 设置批量翻译状态
  batchTranslating.value = true;

  try {
    // 构建API请求数据
    const batchRequest: BatchTranslateItem[] = validItems
      .map((item) => {
        const sourceText = item.translations[config.value.sourceLanguage] || '';

        // 过滤需要翻译的目标语言
        const targetLanguages = config.value.targetLanguages.filter((lang) => {
          return config.value.overwriteExisting || !item.translations[lang];
        });

        return {
          key: item.key,
          source_text: sourceText,
          target_languages: targetLanguages,
        };
      })
      .filter((item) => item.target_languages.length > 0); // 只包含有需要翻译语言的项目

    if (batchRequest.length === 0) {
      alert('没有需要翻译的内容');
      return;
    }

    console.log('开始批量翻译，共', batchRequest.length, '条记录');

    // 调用翻译API
    const response = await translateApiService.startBatchTranslation({
      items: batchRequest,
      source_language: config.value.sourceLanguage,
    });

    if (response.data?.results) {
      // 处理翻译结果
      response.data.results.forEach((result) => {
        if (result.success && result.translations) {
          // 找到对应的表格项
          const tableItem = tableData.value.find(
            (item) => item.key === result.key
          );
          if (tableItem) {
            // 更新翻译结果
            Object.entries(result.translations).forEach(([lang, text]) => {
              tableItem.translations[lang] = text as string;
            });
          }
        } else {
          console.error(`翻译失败 - ${result.key}:`, result.error);
        }
      });

      const summary = response.data.summary;
      alert(`批量翻译完成！成功: ${summary.success}/${summary.total}`);
    }
  } catch (error) {
    console.error('批量翻译失败:', error);
    alert('批量翻译失败，请重试');
  } finally {
    batchTranslating.value = false;
  }
};

// 初始化配置
const initializeConfig = () => {
  if (!props.excelData) return;

  // 设置源语言
  const hasSourceLanguage = global.includeSourceLangList.some((lang) =>
    props.excelData!.languages.includes(lang)
  );

  if (hasSourceLanguage && !config.value.sourceLanguage) {
    config.value.sourceLanguage =
      global.includeSourceLangList.find((lang) =>
        props.excelData!.languages.includes(lang)
      ) || '';
  }

  // 设置目标语言（默认全选除源语言外的所有语言）
  if (config.value.targetLanguages.length === 0) {
    config.value.targetLanguages = props.excelData.languages.filter(
      (lang) => lang !== config.value.sourceLanguage
    );
  }
};

// 监听数据变化
watch(
  () => props.excelData,
  () => {
    if (props.excelData) {
      initializeTableData();
      initializeConfig();
    }
  },
  { immediate: true }
);

// 点击外部关闭下拉框
onMounted(() => {
  document.addEventListener('click', (e) => {
    if (!(e.target as Element).closest('.relative')) {
      showTargetLanguageDropdown.value = false;
    }
  });
});
</script>

<style scoped>
/* 自定义样式 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 固定表格样式 */
.sticky-table-container {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.sticky-table {
  border-collapse: separate;
  border-spacing: 0;
}

/* 固定表头 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 20;
}

.sticky-header th {
  border-bottom: 1px solid #e5e7eb;
}

/* 固定列样式 */
.sticky-column {
  position: sticky;
  z-index: 10;
}

.sticky-column-1 {
  left: 0;
  z-index: 15;
  width: 84px; /* 固定第一列宽度 */
}

.sticky-column-2 {
  /* left: 84px; 第一列的宽度 */
  left: 62px; /* 第一列的宽度 */
  z-index: 15;
  min-width: 192px; /* 固定第二列最小宽度 */
}

/* 表头固定列需要更高的z-index */
.sticky-header .sticky-column {
  z-index: 25;
}

/* 选中行的背景色处理 */
tr.bg-blue-50 .sticky-column {
  background-color: #eff6ff !important;
}

/* 表格滚动条样式 */
.sticky-table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.sticky-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sticky-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sticky-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.sticky-table-container::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 表格边框处理 */
.sticky-table td,
.sticky-table th {
  border-bottom: 1px solid #e5e7eb;
}

.sticky-table tbody tr:last-child td {
  border-bottom: none;
}

/* 阴影效果，增强固定列的视觉效果 */
.sticky-column-1::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
}

.sticky-column-2::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
}
</style>
