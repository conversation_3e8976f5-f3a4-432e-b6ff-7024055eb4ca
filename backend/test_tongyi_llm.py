#!/usr/bin/env python3
"""
通义千问LLM测试脚本
"""

import os
import sys
import logging
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from app.llms import get_llm, LLMType
from app.llms.tongyi_llm import create_tongyi_llm, get_supported_models


def test_basic_generation():
    """测试基本文本生成"""
    print("\n=== 测试基本文本生成 ===")
    
    try:
        # 使用便捷函数创建LLM实例
        llm = get_llm(LLMType.TONGYI)
        
        # 测试简单生成
        prompt = "请用一句话介绍什么是人工智能"
        response = llm.generate(prompt)
        
        print(f"提示词: {prompt}")
        print(f"响应: {response}")
        print("✅ 基本文本生成测试通过")
        
    except Exception as e:
        print(f"❌ 基本文本生成测试失败: {str(e)}")


def test_system_prompt():
    """测试系统提示词"""
    print("\n=== 测试系统提示词 ===")
    
    try:
        llm = create_tongyi_llm()
        
        system_prompt = "你是一个专业的Python编程助手，请用简洁的语言回答问题。"
        user_prompt = "什么是装饰器？"
        
        response = llm.generate(user_prompt, system_prompt)
        
        print(f"系统提示词: {system_prompt}")
        print(f"用户提示词: {user_prompt}")
        print(f"响应: {response}")
        print("✅ 系统提示词测试通过")
        
    except Exception as e:
        print(f"❌ 系统提示词测试失败: {str(e)}")


def test_chat_conversation():
    """测试多轮对话"""
    print("\n=== 测试多轮对话 ===")
    
    try:
        llm = create_tongyi_llm()
        
        messages = [
            {"role": "system", "content": "你是一个友好的助手"},
            {"role": "user", "content": "你好，我想学习Python"},
            {"role": "assistant", "content": "你好！很高兴帮助你学习Python。Python是一门非常适合初学者的编程语言。你想从哪个方面开始学习呢？"},
            {"role": "user", "content": "我想了解Python的基本语法"}
        ]
        
        response = llm.chat(messages)
        
        print("对话历史:")
        for msg in messages:
            print(f"  {msg['role']}: {msg['content']}")
        print(f"助手回复: {response}")
        print("✅ 多轮对话测试通过")
        
    except Exception as e:
        print(f"❌ 多轮对话测试失败: {str(e)}")


def test_translation():
    """测试翻译功能"""
    print("\n=== 测试翻译功能 ===")
    
    try:
        llm = create_tongyi_llm()
        
        # 测试中文到英文翻译
        text = "你好，世界！"
        source_lang = "中文"
        target_lang = "英文"
        
        translation = llm.translate(text, source_lang, target_lang)
        
        print(f"原文 ({source_lang}): {text}")
        print(f"译文 ({target_lang}): {translation}")
        
        # 测试英文到日文翻译
        text2 = "Hello, how are you?"
        source_lang2 = "英文"
        target_lang2 = "日文"
        
        translation2 = llm.translate(text2, source_lang2, target_lang2)
        
        print(f"原文 ({source_lang2}): {text2}")
        print(f"译文 ({target_lang2}): {translation2}")
        
        print("✅ 翻译功能测试通过")
        
    except Exception as e:
        print(f"❌ 翻译功能测试失败: {str(e)}")


def test_different_models():
    """测试不同模型"""
    print("\n=== 测试不同模型 ===")
    
    try:
        print("支持的模型列表:")
        models = get_supported_models()
        for model in models:
            print(f"  - {model}")
        
        # 测试qwen-turbo模型
        llm_turbo = create_tongyi_llm(model="qwen-turbo")
        response = llm_turbo.generate("简单介绍一下Python语言")
        print(f"\nqwen-turbo响应: {response[:100]}...")
        
        print("✅ 不同模型测试通过")
        
    except Exception as e:
        print(f"❌ 不同模型测试失败: {str(e)}")


def test_custom_parameters():
    """测试自定义参数"""
    print("\n=== 测试自定义参数 ===")
    
    try:
        # 测试低温度参数（更确定性的输出）
        llm_low_temp = create_tongyi_llm(temperature=0.1, max_tokens=100)
        response1 = llm_low_temp.generate("1+1等于多少？")
        
        # 测试高温度参数（更随机的输出）
        llm_high_temp = create_tongyi_llm(temperature=0.9, max_tokens=100)
        response2 = llm_high_temp.generate("1+1等于多少？")
        
        print(f"低温度(0.1)响应: {response1}")
        print(f"高温度(0.9)响应: {response2}")
        print("✅ 自定义参数测试通过")
        
    except Exception as e:
        print(f"❌ 自定义参数测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始测试通义千问LLM实现")
    
    # 检查API密钥
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        print("❌ 未找到QWEN_API_KEY环境变量，请先设置API密钥")
        return
    
    print(f"✅ 找到API密钥: {api_key[:10]}...")
    
    # 运行所有测试
    test_basic_generation()
    test_system_prompt()
    test_chat_conversation()
    test_translation()
    test_different_models()
    test_custom_parameters()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    main()
