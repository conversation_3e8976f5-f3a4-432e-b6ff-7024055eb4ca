#!/usr/bin/env python3
"""
通义千问LLM使用示例
演示如何在实际项目中使用通义千问LLM模块
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# 加载环境变量
load_dotenv()

from app.llms import get_llm, LLMType
from app.llms.tongyi_llm import create_tongyi_llm


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 方式1: 使用便捷函数
    llm = get_llm(LLMType.TONGYI)
    
    # 简单对话
    response = llm.generate("请用一句话介绍Python")
    print(f"AI回复: {response}")
    
    print()


def example_translation_service():
    """翻译服务示例"""
    print("=== 翻译服务示例 ===")
    
    llm = create_tongyi_llm()
    
    # 翻译示例
    translations = [
        ("你好，世界！", "中文", "英文"),
        ("Hello, how are you?", "英文", "日文"),
        ("Bonjour", "法文", "中文"),
    ]
    
    for text, source, target in translations:
        try:
            result = llm.translate(text, source, target)
            print(f"{source} -> {target}: {text} => {result}")
        except Exception as e:
            print(f"翻译失败: {e}")
    
    print()


def example_chat_assistant():
    """聊天助手示例"""
    print("=== 聊天助手示例 ===")
    
    llm = create_tongyi_llm(temperature=0.8)
    
    # 模拟多轮对话
    conversation = [
        {"role": "system", "content": "你是一个专业的编程助手，擅长Python开发"},
        {"role": "user", "content": "我想学习Python装饰器"},
        {"role": "assistant", "content": "装饰器是Python的一个强大特性！它允许你在不修改原函数代码的情况下，为函数添加额外功能。你想了解哪个方面？"},
        {"role": "user", "content": "请给我一个简单的装饰器例子"}
    ]
    
    response = llm.chat(conversation)
    print("AI助手回复:")
    print(response)
    
    print()


def example_custom_parameters():
    """自定义参数示例"""
    print("=== 自定义参数示例 ===")
    
    # 创建不同配置的LLM实例
    configs = [
        {"model": "qwen-turbo", "temperature": 0.1, "name": "保守型"},
        {"model": "qwen-turbo", "temperature": 0.9, "name": "创意型"},
    ]
    
    prompt = "请写一个关于春天的短诗"
    
    for config in configs:
        name = config.pop("name")
        llm = create_tongyi_llm(**config)
        
        try:
            response = llm.generate(prompt)
            print(f"{name}回复:")
            print(response)
            print("-" * 50)
        except Exception as e:
            print(f"{name}生成失败: {e}")
    
    print()


def example_error_handling():
    """错误处理示例"""
    print("=== 错误处理示例 ===")
    
    try:
        # 尝试使用无效的API密钥
        llm = create_tongyi_llm(api_key="invalid-key")
        response = llm.generate("测试")
        print(response)
    except ValueError as e:
        print(f"配置错误: {e}")
    except Exception as e:
        print(f"API调用错误: {e}")
    
    print()


def example_batch_translation():
    """批量翻译示例"""
    print("=== 批量翻译示例 ===")
    
    llm = create_tongyi_llm()
    
    # 模拟Excel翻译场景
    translation_data = [
        {"key": "app.title", "zh_CN": "应用标题", "en_US": "", "ja_JP": ""},
        {"key": "app.welcome", "zh_CN": "欢迎使用", "en_US": "", "ja_JP": ""},
        {"key": "button.submit", "zh_CN": "提交", "en_US": "", "ja_JP": ""},
        {"key": "button.cancel", "zh_CN": "取消", "en_US": "", "ja_JP": ""},
    ]
    
    # 批量翻译中文到英文和日文
    for item in translation_data:
        source_text = item["zh_CN"]
        
        try:
            # 翻译到英文
            if not item["en_US"]:
                item["en_US"] = llm.translate(source_text, "中文", "英文")
            
            # 翻译到日文
            if not item["ja_JP"]:
                item["ja_JP"] = llm.translate(source_text, "中文", "日文")
                
            print(f"Key: {item['key']}")
            print(f"  中文: {item['zh_CN']}")
            print(f"  英文: {item['en_US']}")
            print(f"  日文: {item['ja_JP']}")
            print()
            
        except Exception as e:
            print(f"翻译 {item['key']} 失败: {e}")
    
    print()


def main():
    """主函数"""
    print("🚀 通义千问LLM使用示例")
    print("=" * 50)
    
    # 检查API密钥
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        print("❌ 未找到QWEN_API_KEY环境变量")
        print("请在.env文件中设置: QWEN_API_KEY=your-api-key")
        return
    
    print(f"✅ API密钥已配置: {api_key[:10]}...")
    print()
    
    # 运行示例
    try:
        example_basic_usage()
        example_translation_service()
        example_chat_assistant()
        example_custom_parameters()
        example_batch_translation()
        example_error_handling()
    except KeyboardInterrupt:
        print("\n👋 示例已停止")
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
    
    print("🎉 示例演示完成！")


if __name__ == "__main__":
    main()
