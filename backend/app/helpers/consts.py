import os
from dotenv import load_dotenv, find_dotenv

# 加载环境变量
load_dotenv(find_dotenv())

# 定义可导出的常量列表
__all__ = ["DASHSCOPE_API_KEY", "DASHSCOPE_MODEL", "DASHSCOPE_TEMPERATURE"]

# 环境变量配置
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")
DASHSCOPE_MODEL = os.getenv("DASHSCOPE_MODEL", "qwen-turbo")
DASHSCOPE_TEMPERATURE = float(os.getenv("DASHSCOPE_TEMPERATURE", "0.7"))

print("DASHSCOPE_API_KEY", DASHSCOPE_API_KEY, os.getenv("DASHSCOPE_API_KEY"))
