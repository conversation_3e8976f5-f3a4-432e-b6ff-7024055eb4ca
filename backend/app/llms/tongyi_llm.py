"""
通义千问大模型实现
基于 langchain_community 中的 ChatTongyi
"""

import os
import logging
from typing import Optional, Dict, List

from app.helpers.consts import DASHSCOPE_API_KEY, DASHSCOPE_MODEL, DASHSCOPE_TEMPERATURE
from langchain_community.chat_models import ChatTongyi

# from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

logger = logging.getLogger(__name__)


class TongyiLLM:
    """
    通义千问大模型封装类

    提供统一的文本生成接口，支持单轮对话和多轮对话
    基于 langchain_community.chat_models.ChatTongyi 实现
    """

    def __init__(
        self,
        api_key: str = DASHSCOPE_API_KEY,
        model: str = DASHSCOPE_MODEL,
        temperature: float = DASHSCOPE_TEMPERATURE,
        max_tokens: int = 2000,
        top_p: float = 0.8,
        **kwargs,
    ):
        print(DASHSCOPE_API_KEY)
        """
        初始化通义千问模型

        Args:
            api_key: DashScope API密钥
            model: 模型名称
            temperature: 温度参数，控制生成的随机性
            max_tokens: 最大生成token数
            top_p: Top-p采样参数
            **kwargs: 其他参数
        """
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

        if not self.api_key:
            raise ValueError(
                "未找到通义千问API密钥，请设置DASHSCOPE_API_KEY环境变量或传入api_key参数"
            )

        # 初始化 ChatTongyi 实例
        self.chat_model = ChatTongyi(
            dashscope_api_key=self.api_key,
            model_name=self.model,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            top_p=self.top_p,
            **kwargs,
        )

        logger.info(f"通义千问模型初始化完成，使用模型: {self.model}")
