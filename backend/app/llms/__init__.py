"""
大模型管理模块
提供统一的大模型接口
"""

from enum import Enum
from typing import Optional
import logging

from app.llms.tongyi_llm import TongyiLLM

logger = logging.getLogger(__name__)


class LLMType(Enum):
    """大模型类型枚举"""

    TONGYI = "tongyi"
    # 可以在这里添加更多模型类型
    # OPENAI = "openai"
    # CLAUDE = "claude"


def get_llm(llm_type: LLMType = LLMType.TONGYI, **kwargs):
    """
    获取大模型实例的便捷函数

    Args:
        llm_type: 模型类型，默认为通义千问
        **kwargs: 模型初始化参数

    Returns:
        大模型实例

    Raises:
        ValueError: 不支持的模型类型
    """
    if llm_type == LLMType.TONGYI:
        tonyLLM = TongyiLLM(**kwargs)
        return tonyLLM.chat_model
    else:
        raise ValueError(f"不支持的模型类型: {llm_type}")


# 导出主要接口
__all__ = [
    "LLMType",
    "get_llm",
    "create_tongyi_llm",
]
