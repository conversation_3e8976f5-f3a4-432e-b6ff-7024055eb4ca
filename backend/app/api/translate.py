"""
翻译API接口层 - 简化版本，只保留上传接口
"""

# 保存上传的文件到临时目录
import tempfile
import os
from flask import Blueprint, request, jsonify
import logging
from app.services.excel_service import ExcelService
from app.services.translate_service import TranslateService
from app.services.task_service import translation_task_service
from app.helpers.task_manager import TaskManager

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
translate_bp = Blueprint("translate", __name__)


def _convert_single_to_batch(request_data):
    """
    将单条翻译请求转换为批量翻译格式

    Args:
        request_data: 单条翻译请求数据

    Returns:
        tuple: (items, source_language)

    Raises:
        ValueError: 当请求数据格式不正确时
    """
    # 验证必需参数
    if "text" not in request_data:
        raise ValueError("缺少text参数")

    if "target_lang" not in request_data:
        raise ValueError("缺少target_lang参数")

    text = request_data["text"]
    target_lang = request_data["target_lang"]
    source_lang = request_data.get("source_lang", "en-US")

    # 验证参数
    if not text or not text.strip():
        raise ValueError("文本内容不能为空")

    if not target_lang or not target_lang.strip():
        raise ValueError("目标语言不能为空")

    # 转换为批量格式
    items = [
        {
            "key": f"single_translate_{hash(text)}",  # 生成唯一key
            "source_text": text,
            "target_languages": [target_lang],
        }
    ]

    return items, source_lang


@translate_bp.route("/translate/upload", methods=["POST"])
def upload_excel():
    """Excel文件上传解析接口 - 只解析不翻译"""
    try:
        # 检查是否有文件上传
        if "file" not in request.files:
            return jsonify({"code": -1, "message": "没有上传文件"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"code": -1, "message": "文件名为空"}), 400

        excel_service = ExcelService()
        translate_service = TranslateService()

        logger.info(f"开始处理上传文件: {file.filename}")

        file_path = excel_service.create_temp_file(file)
        logger.info(f"临时文件路径: {file_path}")

        # 解析Excel文件
        parse_result = excel_service.parse_excel_to_translation_tasks(file_path)
        logger.info(f"Excel解析完成，共 {len(parse_result['list'])} 条记录")

        # 翻译语言代码为中文名称
        language_names = {}
        if parse_result.get("target_language_list"):
            try:
                language_names_json = translate_service.translate_language_code_list(
                    parse_result["target_language_list"]
                )
                import json

                language_names = json.loads(language_names_json)
                logger.info(f"语言名称翻译完成: {language_names}")
            except Exception as e:
                logger.warning(f"语言名称翻译失败: {str(e)}")
                # 使用默认的语言代码作为名称
                for lang_info in parse_result["target_language_list"]:
                    language_names[lang_info["code"]] = lang_info["code"]

        # 转换为前端需要的格式
        languages = [parse_result["source_langauge"]]  # 源语言放在第一位
        for lang_info in parse_result["target_language_list"]:
            languages.append(lang_info["code"])

        # 构建翻译数据字典
        translations = {}
        for task in parse_result["list"]:
            key = task["key"]
            translations[key] = {}

            # 添加源语言内容
            translations[key][parse_result["source_langauge"]] = task[
                "source_language_context"
            ]

            # 添加目标语言内容（初始为空）
            for translate_item in task["translate_list"]:
                translations[key][translate_item["language"]] = translate_item[
                    "content"
                ]

        # 构建元数据
        metadata = {
            "filename": file.filename,
            "total_keys": len(parse_result["list"]),
            "total_languages": len(languages),
            "sheet_name": "Sheet1",  # 默认工作表名
        }

        # 构建前端需要的数据格式
        result_data = {
            "languages": languages,
            "language_names": language_names,
            "translations": translations,
            "metadata": metadata,
        }

        # 删除临时文件
        os.remove(file_path)

        return jsonify({"code": 0, "message": "文件解析成功", "data": result_data})

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500


@translate_bp.route("/translate/batch", methods=["POST"])
def batch_translate():
    """统一翻译接口 - 支持单条和批量翻译，异步处理，立即返回task_id"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({"code": -1, "message": "请求数据为空"}), 400

        # 检查是单条翻译还是批量翻译
        if "text" in request_data:
            # 单条翻译格式：{"text": "...", "target_lang": "...", "source_lang": "..."}
            try:
                items, source_language = _convert_single_to_batch(request_data)
            except ValueError as e:
                return jsonify({"code": -1, "message": str(e)}), 400
        elif "items" in request_data:
            # 批量翻译格式：{"items": [...], "source_language": "..."}
            items = request_data["items"]
            if not isinstance(items, list) or len(items) == 0:
                return jsonify({"code": -1, "message": "items必须是非空数组"}), 400
            source_language = request_data.get("source_language", "en-US")
        else:
            return (
                jsonify(
                    {
                        "code": -1,
                        "message": "请求格式错误：需要提供 'text' 或 'items' 参数",
                    }
                ),
                400,
            )

        # 验证每个item的必需字段
        for item in items:
            if (
                "key" not in item
                or "source_text" not in item
                or "target_languages" not in item
            ):
                return (
                    jsonify(
                        {
                            "code": -1,
                            "message": "缺少必需字段: key, source_text, target_languages",
                        }
                    ),
                    400,
                )

            if (
                not isinstance(item["target_languages"], list)
                or len(item["target_languages"]) == 0
            ):
                return (
                    jsonify({"code": -1, "message": "target_languages必须是非空数组"}),
                    400,
                )

        # 生成任务ID
        task_manager = TaskManager()
        task_id = task_manager.generate_task_id()

        # 启动异步翻译任务
        success = translation_task_service.start_batch_translation(
            task_id=task_id, items=items, source_language=source_language
        )

        if not success:
            return jsonify({"code": -1, "message": "启动翻译任务失败"}), 500

        logger.info(f"批量翻译任务已启动: {task_id}, 共 {len(items)} 条记录")

        # 立即返回task_id
        return jsonify(
            {
                "code": 0,
                "message": "翻译任务已启动",
                "data": {
                    "task_id": task_id,
                    "total_items": len(items),
                    "status": "processing",
                },
            }
        )

    except Exception as e:
        logger.error(f"批量翻译处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500


@translate_bp.route("/translate", methods=["POST"])
def translate():
    """统一翻译接口 - 支持单条和批量翻译的别名路由"""
    return batch_translate()
