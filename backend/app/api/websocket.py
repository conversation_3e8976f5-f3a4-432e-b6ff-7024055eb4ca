"""
WebSocket API接口层 - 处理WebSocket连接和消息推送
"""

import logging
from flask import request
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room, disconnect

logger = logging.getLogger(__name__)


# WebSocket连接管理
class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.connections = {}  # task_id -> set(session_ids)

    def add_connection(self, task_id: str, session_id: str):
        """添加连接"""
        if task_id not in self.connections:
            self.connections[task_id] = set()
        self.connections[task_id].add(session_id)
        logger.info(f"添加WebSocket连接: task_id={task_id}, session_id={session_id}")

    def remove_connection(self, task_id: str, session_id: str):
        """移除连接"""
        if task_id in self.connections:
            self.connections[task_id].discard(session_id)
            if not self.connections[task_id]:
                del self.connections[task_id]
        logger.info(f"移除WebSocket连接: task_id={task_id}, session_id={session_id}")

    def get_connections(self, task_id: str) -> set:
        """获取任务的所有连接"""
        return self.connections.get(task_id, set())

    def remove_session(self, session_id: str):
        """移除会话的所有连接"""
        for task_id in list(self.connections.keys()):
            self.connections[task_id].discard(session_id)
            if not self.connections[task_id]:
                del self.connections[task_id]


# 全局WebSocket管理器实例
ws_manager = WebSocketManager()


def init_websocket(app, socketio: SocketIO):
    """初始化WebSocket事件处理器"""

    @socketio.on("connect")
    def handle_connect():
        """处理WebSocket连接"""
        logger.info(f"WebSocket连接建立: {request.sid}")
        emit("connected", {"status": "success", "message": "连接成功"})

    @socketio.on("disconnect")
    def handle_disconnect():
        """处理WebSocket断开连接"""
        logger.info(f"WebSocket连接断开: {request.sid}")
        # 清理该会话的所有连接记录
        ws_manager.remove_session(request.sid)

    @socketio.on("join_task")
    def handle_join_task(data):
        """处理加入任务房间"""
        try:
            task_id = data.get("task_id")
            if not task_id:
                emit("error", {"message": "缺少task_id参数"})
                return

            # 加入房间
            join_room(task_id)
            ws_manager.add_connection(task_id, request.sid)

            logger.info(
                f"客户端加入任务房间: task_id={task_id}, session_id={request.sid}"
            )
            emit("joined_task", {"task_id": task_id, "status": "success"})

        except Exception as e:
            logger.error(f"处理join_task失败: {str(e)}")
            emit("error", {"message": f"加入任务失败: {str(e)}"})

    @socketio.on("leave_task")
    def handle_leave_task(data):
        """处理离开任务房间"""
        try:
            task_id = data.get("task_id")
            if not task_id:
                emit("error", {"message": "缺少task_id参数"})
                return

            # 离开房间
            leave_room(task_id)
            ws_manager.remove_connection(task_id, request.sid)

            logger.info(
                f"客户端离开任务房间: task_id={task_id}, session_id={request.sid}"
            )
            emit("left_task", {"task_id": task_id, "status": "success"})

        except Exception as e:
            logger.error(f"处理leave_task失败: {str(e)}")
            emit("error", {"message": f"离开任务失败: {str(e)}"})

    @socketio.on("ping")
    def handle_ping():
        """处理心跳检测"""
        emit("pong", {"timestamp": request.sid})
