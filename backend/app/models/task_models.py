"""
任务相关的数据模型和枚举定义
"""

from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime


class TaskItemStatus(Enum):
    """翻译任务单个条目的状态枚举"""

    PROCESSING = "processing"  # 翻译中
    SUCCESS = "success"  # 翻译成功
    FAILED = "failed"  # 翻译失败（包括超时）


class TaskItemData(BaseModel):
    """翻译任务单个条目的数据结构"""

    status: TaskItemStatus
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    updated_at: datetime


class TranslationTaskData(BaseModel):
    """翻译任务的完整数据结构"""

    task_id: str
    status: str  # "processing" | "completed"
    total_items: int
    completed_items: int = 0
    failed_items: int = 0
    items: Dict[str, TaskItemData]
    created_at: datetime
    updated_at: datetime


class TranslationRequest(BaseModel):
    """批量翻译请求数据结构"""

    items: list[Dict[str, Any]]
    source_language: Optional[str] = "en-US"


class WebSocketMessage(BaseModel):
    """WebSocket消息数据结构"""

    type: str = "translation_update"
    task_id: str
    item_key: str
    status: TaskItemStatus
    result: Optional[Dict[str, Any]] = None
    progress: Dict[str, int]
    error_message: Optional[str] = None
