"""
示例文件：展示如何在其他Python文件中使用consts.py中的常量
"""

# 方法1：导入所有常量
from backend.app.helpers.consts import *

def example_usage_method1():
    """使用方法1：导入所有常量"""
    print("=== 方法1：导入所有常量 ===")
    print(f"API Key: {DASHSCOPE_API_KEY}")
    print(f"Model: {DASHSCOPE_MODEL}")
    print(f"Temperature: {DASHSCOPE_TEMPERATURE}")
    print()


# 方法2：导入特定常量
from backend.app.helpers.consts import DASHSCOPE_API_KEY, DASHSCOPE_MODEL, DASHSCOPE_TEMPERATURE

def example_usage_method2():
    """使用方法2：导入特定常量"""
    print("=== 方法2：导入特定常量 ===")
    print(f"API Key: {DASHSCOPE_API_KEY}")
    print(f"Model: {DASHSCOPE_MODEL}")
    print(f"Temperature: {DASHSCOPE_TEMPERATURE}")
    print()


# 方法3：导入模块并使用点号访问
from backend.app.helpers import consts

def example_usage_method3():
    """使用方法3：导入模块并使用点号访问"""
    print("=== 方法3：导入模块并使用点号访问 ===")
    print(f"API Key: {consts.DASHSCOPE_API_KEY}")
    print(f"Model: {consts.DASHSCOPE_MODEL}")
    print(f"Temperature: {consts.DASHSCOPE_TEMPERATURE}")
    print()


# 方法4：导入模块并重命名
from backend.app.helpers import consts as config

def example_usage_method4():
    """使用方法4：导入模块并重命名"""
    print("=== 方法4：导入模块并重命名 ===")
    print(f"API Key: {config.DASHSCOPE_API_KEY}")
    print(f"Model: {config.DASHSCOPE_MODEL}")
    print(f"Temperature: {config.DASHSCOPE_TEMPERATURE}")
    print()


def create_api_client():
    """实际使用场景示例：创建API客户端"""
    from backend.app.helpers.consts import DASHSCOPE_API_KEY, DASHSCOPE_MODEL, DASHSCOPE_TEMPERATURE
    
    if not DASHSCOPE_API_KEY:
        raise ValueError("DASHSCOPE_API_KEY 环境变量未设置")
    
    # 模拟创建API客户端的配置
    client_config = {
        "api_key": DASHSCOPE_API_KEY,
        "model": DASHSCOPE_MODEL,
        "temperature": DASHSCOPE_TEMPERATURE
    }
    
    print("=== 实际使用场景示例 ===")
    print(f"API客户端配置: {client_config}")
    return client_config


if __name__ == "__main__":
    # 演示所有使用方法
    example_usage_method1()
    example_usage_method2()
    example_usage_method3()
    example_usage_method4()
    create_api_client()
