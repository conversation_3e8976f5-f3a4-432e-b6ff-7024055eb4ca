"""
Redis服务层 - 处理Redis连接和任务数据操作
"""

import json
import redis
from typing import Optional, Dict, Any
from datetime import datetime
import logging

from app.config.settings import settings
from app.models.task_models import TranslationTaskData, TaskItemData, TaskItemStatus

logger = logging.getLogger(__name__)


class RedisService:
    """Redis服务类"""

    def __init__(self):
        """初始化Redis连接"""
        self._redis_client = None
        self._connect()

    def _connect(self):
        """建立Redis连接"""
        try:
            self._redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
            )
            # 测试连接
            self._redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            raise

    def _get_task_key(self, task_id: str) -> str:
        """获取任务的Redis key"""
        return f"task:{task_id}"

    def _serialize_task_data(self, task_data: TranslationTaskData) -> str:
        """序列化任务数据为JSON字符串"""
        # 转换为字典，处理枚举和datetime
        data_dict = {
            "task_id": task_data.task_id,
            "status": task_data.status,
            "total_items": task_data.total_items,
            "completed_items": task_data.completed_items,
            "failed_items": task_data.failed_items,
            "items": {},
            "created_at": task_data.created_at.isoformat(),
            "updated_at": task_data.updated_at.isoformat(),
        }

        # 序列化items
        for key, item_data in task_data.items.items():
            data_dict["items"][key] = {
                "status": item_data.status.value,
                "result": item_data.result,
                "error_message": item_data.error_message,
                "updated_at": item_data.updated_at.isoformat(),
            }

        return json.dumps(data_dict, ensure_ascii=False)

    def _deserialize_task_data(self, json_str: str) -> TranslationTaskData:
        """从JSON字符串反序列化任务数据"""
        data_dict = json.loads(json_str)

        # 重建items
        items = {}
        for key, item_dict in data_dict["items"].items():
            items[key] = TaskItemData(
                status=TaskItemStatus(item_dict["status"]),
                result=item_dict["result"],
                error_message=item_dict["error_message"],
                updated_at=datetime.fromisoformat(item_dict["updated_at"]),
            )

        return TranslationTaskData(
            task_id=data_dict["task_id"],
            status=data_dict["status"],
            total_items=data_dict["total_items"],
            completed_items=data_dict["completed_items"],
            failed_items=data_dict["failed_items"],
            items=items,
            created_at=datetime.fromisoformat(data_dict["created_at"]),
            updated_at=datetime.fromisoformat(data_dict["updated_at"]),
        )

    def create_task(self, task_data: TranslationTaskData) -> bool:
        """创建新任务记录"""
        try:
            task_key = self._get_task_key(task_data.task_id)
            serialized_data = self._serialize_task_data(task_data)

            # 设置任务数据，带TTL
            self._redis_client.setex(
                task_key, settings.TASK_TTL_SECONDS, serialized_data
            )

            logger.info(f"创建任务记录成功: {task_data.task_id}")
            return True
        except Exception as e:
            logger.error(f"创建任务记录失败: {str(e)}")
            return False

    def get_task(self, task_id: str) -> Optional[TranslationTaskData]:
        """获取任务数据"""
        try:
            task_key = self._get_task_key(task_id)
            json_str = self._redis_client.get(task_key)

            if json_str is None:
                return None

            return self._deserialize_task_data(json_str)
        except Exception as e:
            logger.error(f"获取任务数据失败: {str(e)}")
            return None

    def update_task(self, task_data: TranslationTaskData) -> bool:
        """更新任务数据"""
        try:
            task_key = self._get_task_key(task_data.task_id)
            serialized_data = self._serialize_task_data(task_data)

            # 更新任务数据，保持原有TTL
            ttl = self._redis_client.ttl(task_key)
            if ttl > 0:
                self._redis_client.setex(task_key, ttl, serialized_data)
            else:
                # 如果没有TTL或已过期，设置默认TTL
                self._redis_client.setex(
                    task_key, settings.TASK_TTL_SECONDS, serialized_data
                )

            return True
        except Exception as e:
            logger.error(f"更新任务数据失败: {str(e)}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """删除任务记录"""
        try:
            task_key = self._get_task_key(task_id)
            self._redis_client.delete(task_key)
            logger.info(f"删除任务记录成功: {task_id}")
            return True
        except Exception as e:
            logger.error(f"删除任务记录失败: {str(e)}")
            return False

    def task_exists(self, task_id: str) -> bool:
        """检查任务是否存在"""
        try:
            task_key = self._get_task_key(task_id)
            return self._redis_client.exists(task_key) > 0
        except Exception as e:
            logger.error(f"检查任务存在性失败: {str(e)}")
            return False


# 全局Redis服务实例
redis_service = RedisService()
