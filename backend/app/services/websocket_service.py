"""
WebSocket服务层 - 处理消息推送和连接管理
"""

import logging
from typing import Dict, Any
from flask_socketio import <PERSON>cket<PERSON>, emit

from app.models.task_models import WebSocketMessage, TaskItemStatus

logger = logging.getLogger(__name__)


class WebSocketService:
    """WebSocket服务类"""

    def __init__(self, socketio: SocketIO = None):
        self.socketio = socketio

    def set_socketio(self, socketio: SocketIO):
        """设置SocketIO实例"""
        self.socketio = socketio

    def send_translation_update(
        self,
        task_id: str,
        item_key: str,
        status: TaskItemStatus,
        result: Dict[str, Any] = None,
        progress: Dict[str, int] = None,
        error_message: str = None,
    ):
        """发送翻译更新消息"""
        if not self.socketio:
            logger.error("SocketIO未初始化")
            return False

        try:
            # 构建消息
            message = WebSocketMessage(
                type="translation_update",
                task_id=task_id,
                item_key=item_key,
                status=status,
                result=result,
                progress=progress or {},
                error_message=error_message,
            )

            # 发送到任务房间
            self.socketio.emit("translation_update", message.model_dump(), room=task_id)

            logger.info(
                f"发送翻译更新消息: task_id={task_id}, item_key={item_key}, status={status.value}"
            )
            return True

        except Exception as e:
            logger.error(f"发送翻译更新消息失败: {str(e)}")
            return False

    def send_task_completed(self, task_id: str, summary: Dict[str, Any]):
        """发送任务完成消息"""
        if not self.socketio:
            logger.error("SocketIO未初始化")
            return False

        try:
            message = {"type": "task_completed", "task_id": task_id, "summary": summary}

            self.socketio.emit("task_completed", message, room=task_id)
            logger.info(f"发送任务完成消息: task_id={task_id}")
            return True

        except Exception as e:
            logger.error(f"发送任务完成消息失败: {str(e)}")
            return False

    def send_error_message(self, task_id: str, error_message: str):
        """发送错误消息"""
        if not self.socketio:
            logger.error("SocketIO未初始化")
            return False

        try:
            message = {"type": "error", "task_id": task_id, "message": error_message}

            self.socketio.emit("error", message, room=task_id)
            logger.info(f"发送错误消息: task_id={task_id}")
            return True

        except Exception as e:
            logger.error(f"发送错误消息失败: {str(e)}")
            return False


# 全局WebSocket服务实例
websocket_service = WebSocketService()
