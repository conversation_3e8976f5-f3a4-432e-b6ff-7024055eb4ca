"""
Excel 服务模块
用于解析 Excel 文件并转换为翻译任务的 JSON 格式
"""

import tempfile
import os
import logging
from typing import List, Dict, Any, Optional
import pandas as pd
from pathlib import Path
from werkzeug.datastructures import FileStorage
from flask import jsonify

logger = logging.getLogger(__name__)


class ExcelService:
    """
    Excel 文件处理服务

    负责解析 Excel 文件并转换为翻译任务所需的 JSON 格式
    """

    ignore_language_list = ["zh-CN", "zh"]
    source_langauge_list = ["en-US", "en"]
    current_source_language = "en-US"

    def __init__(self):
        """初始化 ExcelService"""
        logger.info("ExcelService 初始化完成")

    def create_temp_file(self, file: FileStorage):
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, file.filename)
        file.save(temp_file_path)
        return temp_file_path

    def parse_excel_to_task_list(
        self,
        file_path: str,
        sheet_name: Optional[str] = None,
    ):
        logger.info(f"开始解析 Excel 文件: {file_path}")

        # 读取 Excel 文件
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        else:
            df = pd.read_excel(file_path, header=None)

        # 检查文件是否为空
        if df.empty or df.shape[0] < 2 or df.shape[1] < 2:
            raise ValueError("Excel 文件格式不正确：至少需要 2 行 2 列数据")

        # 获取语言列表（第一行从第二列开始）
        languages = []
        for col_idx in range(1, df.shape[1]):
            lang = df.iloc[0, col_idx]
            if pd.notna(lang) and str(lang).strip():
                languages.append(str(lang).strip())
            else:
                # 如果遇到空列，停止读取语言
                break

        if not languages:
            raise ValueError("未找到有效的语言列")

        logger.info(f"检测到语言: {languages}")

        # 确定源语言和目标语言
        source_language = self.current_source_language
        target_languages = []

        for language in languages:
            if language in self.source_langauge_list:
                source_language = language
                continue
            elif language in self.ignore_language_list:
                continue
            else:
                target_languages.append({"code": language, "name": ""})

        # 解析翻译任务
        translation_tasks = []

        for row_idx in range(1, df.shape[0]):
            # 获取 key（第一列）
            key = df.iloc[row_idx, 0]
            if pd.isna(key) or not str(key).strip():
                logger.warning(f"第 {row_idx + 1} 行的 key 为空，跳过")
                continue

            key = str(key).strip()

            # 获取源文本
            source_text = ""
            source_col_idx = None

            # 找到源语言列的索引
            for lang_idx, language in enumerate(languages):
                if language in self.source_langauge_list:
                    source_col_idx = lang_idx + 1  # 语言列从第二列开始
                    break
                elif language in self.ignore_language_list:
                    break

            # 如果没有找到源语言列，默认使用第二列
            if source_col_idx is None:
                source_col_idx = 1

            if source_col_idx < df.shape[1]:
                source_cell = df.iloc[row_idx, source_col_idx]
                if pd.notna(source_cell):
                    source_text = str(source_cell).strip()

            # 如果源文本为空，跳过这一行
            if not source_text:
                logger.warning(f"第 {row_idx + 1} 行的源文本为空，跳过")
                continue

            # 创建翻译列表
            translate_list = []

            for lang_idx, language in enumerate(languages):
                col_idx = lang_idx + 1  # 语言列从第二列开始

                # 跳过源语言列
                if (
                    language in self.source_langauge_list
                    or language in self.ignore_language_list
                ):
                    continue

                # 获取翻译文本
                content = ""
                if col_idx < df.shape[1]:
                    translation_cell = df.iloc[row_idx, col_idx]
                    if pd.notna(translation_cell):
                        content = str(translation_cell).strip()

                translate_list.append({"language": language, "content": content})

            # 创建翻译任务
            task = {
                "key": key,
                "source_language": source_language,
                "source_language_context": source_text,
                "translate_list": translate_list,
            }

            translation_tasks.append(task)
            logger.debug(f"创建翻译任务: {key}")

        # 返回指定格式的结果
        result = {
            "source_langauge": source_language,
            "target_language_list": target_languages,
            "list": translation_tasks,
        }

        return result

    def parse_excel_to_translation_tasks(
        self,
        file_path: str,
        source_language: str = "en-US",
        sheet_name: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        解析 Excel 文件为翻译任务列表

        Excel 格式规则：
        - 第一行从第二列开始都是语言信息
        - 第一列从第二行开始都是 key
        - 如果单元格内容为空，则用空字符串来标识

        Args:
            file_path: Excel 文件路径
            source_language: 源语言代码，默认为 "en-US"
            sheet_name: 工作表名称，如果为 None 则使用第一个工作表

        Returns:
            List[Dict]: 翻译任务列表

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: Excel 格式不正确
            Exception: 其他解析错误
        """
        try:
            # 检查文件是否存在
            if not Path(file_path).exists():
                raise FileNotFoundError(f"Excel 文件不存在: {file_path}")

            translation_tasks = self.parse_excel_to_task_list(
                file_path=file_path, sheet_name=sheet_name
            )
            logger.info(f"translation_tasks - {translation_tasks}")
            return translation_tasks

        except FileNotFoundError:
            raise
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"解析 Excel 文件失败: {str(e)}")
            raise Exception(f"解析 Excel 文件失败: {str(e)}")

    def get_excel_sheets(self, file_path: str) -> List[str]:
        """
        获取 Excel 文件中的所有工作表名称

        Args:
            file_path: Excel 文件路径

        Returns:
            List[str]: 工作表名称列表
        """
        try:
            if not Path(file_path).exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            excel_file = pd.ExcelFile(file_path)
            return excel_file.sheet_names

        except Exception as e:
            logger.error(f"获取工作表列表失败: {str(e)}")
            raise Exception(f"获取工作表列表失败: {str(e)}")
