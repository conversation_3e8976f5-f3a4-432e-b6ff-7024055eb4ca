#!/usr/bin/env python3
"""
测试后端服务启动脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from app.main import create_app

    print("✅ 所有模块导入成功")

    # 创建应用
    app, socketio = create_app()
    print("✅ Flask和SocketIO应用创建成功")

    print("🎉 后端服务准备就绪！")
    print("如需启动服务，请运行: python -m app.main")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 其他错误: {e}")
    sys.exit(1)
